# ⚙️ HyAIAgent 配置与部署指南

## 📋 环境要求

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Python版本**: Python 3.9+
- **内存**: 最低 4GB RAM，推荐 8GB+
- **存储**: 最低 2GB 可用空间
- **网络**: 稳定的互联网连接（用于AI API调用）

### 依赖包清单
```txt
# 核心框架
asyncio>=3.4.3
aiohttp>=3.8.0
aiofiles>=0.8.0
pydantic>=1.10.0
fastapi>=0.95.0
uvicorn>=0.20.0

# 数据库支持
aiosqlite>=0.18.0
asyncpg>=0.27.0
sqlalchemy>=2.0.0
alembic>=1.10.0

# 缓存和队列
redis>=4.5.0
celery>=5.2.0

# GUI界面
PyQt6>=6.4.0
PyQt6-tools>=6.4.0

# 文档处理
python-docx>=0.8.11
openpyxl>=3.1.0
PyPDF2>=3.0.0

# 网络搜索
requests>=2.28.0
beautifulsoup4>=4.11.0
selenium>=4.8.0

# 工具库
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.3.0
loguru>=0.6.0

# 测试
pytest>=7.2.0
pytest-asyncio>=0.20.0
pytest-cov>=4.0.0
```

## 🔧 配置文件结构

### 主配置文件 (config/default_config.json)
```json
{
  "application": {
    "name": "HyAIAgent",
    "version": "1.0.0",
    "debug": false,
    "log_level": "INFO"
  },
  "ai_api": {
    "provider": "openai",
    "api_url": "https://api.openai.com/v1/chat/completions",
    "api_key": "${OPENAI_API_KEY}",
    "model": "gpt-4",
    "max_tokens": 4000,
    "temperature": 0.7,
    "timeout": 30,
    "retry_attempts": 3,
    "retry_delay": 1
  },
  "database": {
    "type": "sqlite",
    "connection_string": "data/hyaiagent.db",
    "pool_size": 10,
    "max_overflow": 20,
    "echo": false
  },
  "cache": {
    "type": "redis",
    "host": "localhost",
    "port": 6379,
    "db": 0,
    "password": null,
    "ttl": 3600
  },
  "file_operations": {
    "base_path": "./workspace",
    "allowed_extensions": [".txt", ".md", ".json", ".py", ".js", ".html", ".css"],
    "max_file_size": 10485760,
    "backup_enabled": true,
    "backup_directory": "./backups"
  },
  "web_search": {
    "default_engine": "google",
    "max_results": 10,
    "timeout": 10,
    "user_agent": "HyAIAgent/1.0.0"
  },
  "task_management": {
    "max_concurrent_tasks": 5,
    "task_timeout": 300,
    "cleanup_interval": 3600,
    "max_task_history": 1000
  },
  "security": {
    "enable_sandbox": true,
    "allowed_domains": ["*.openai.com", "*.anthropic.com"],
    "max_request_size": 1048576,
    "rate_limit": {
      "requests_per_minute": 60,
      "requests_per_hour": 1000
    }
  },
  "ui": {
    "theme": "default",
    "window_size": [1200, 800],
    "auto_save": true,
    "auto_save_interval": 30
  }
}
```

### AI提示词配置 (config/ai_prompts.json)
```json
{
  "system_prompt": {
    "base": "你是HyAIAgent框架中的智能决策引擎。你的任务是分析用户输入，制定执行计划，并返回结构化的JSON响应。",
    "capabilities": "你可以执行文件操作、数据库查询、网络搜索、任务管理等操作。",
    "constraints": "请始终保持安全、高效、准确的原则。",
    "output_format": "请严格按照预定义的JSON格式返回响应。"
  },
  "task_analysis_prompt": "请分析以下任务的复杂度、所需资源和执行策略：",
  "error_handling_prompt": "发生错误时，请提供详细的错误分析和解决建议：",
  "optimization_prompt": "请分析当前执行计划并提供优化建议："
}
```

### 操作模式配置 (config/operation_schemas.json)
```json
{
  "file_operations": {
    "read": {
      "required_params": ["file_path"],
      "optional_params": ["encoding"],
      "description": "读取文件内容"
    },
    "write": {
      "required_params": ["file_path", "content"],
      "optional_params": ["encoding", "backup"],
      "description": "写入文件内容"
    },
    "delete": {
      "required_params": ["file_path"],
      "optional_params": [],
      "description": "删除文件"
    }
  },
  "database_operations": {
    "query": {
      "required_params": ["query"],
      "optional_params": ["params"],
      "description": "执行数据库查询"
    },
    "insert": {
      "required_params": ["table", "data"],
      "optional_params": ["on_conflict"],
      "description": "插入数据"
    }
  },
  "web_search_operations": {
    "search": {
      "required_params": ["query"],
      "optional_params": ["max_results", "engine"],
      "description": "执行网络搜索"
    }
  }
}
```

## 🚀 部署步骤

### 1. 环境准备
```bash
# 创建项目目录
mkdir HyAIAgent
cd HyAIAgent

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2. 安装依赖
```bash
# 安装核心依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install -r requirements-dev.txt
```

### 3. 环境变量配置
创建 `.env` 文件：
```env
# AI API配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///data/hyaiagent.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
DEBUG=false
LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1
```

### 4. 数据库初始化
```bash
# 创建数据库表
python -m alembic upgrade head

# 或者使用初始化脚本
python scripts/init_database.py
```

### 5. 配置验证
```bash
# 验证配置
python scripts/validate_config.py

# 测试AI API连接
python scripts/test_ai_connection.py

# 测试数据库连接
python scripts/test_database.py
```

## 🐳 Docker部署

### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs workspace backups

# 设置权限
RUN chmod +x scripts/*.py

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  hyaiagent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************/hyaiagent
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./workspace:/app/workspace
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: hyaiagent
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 部署命令
```bash
# 构建和启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f hyaiagent

# 停止服务
docker-compose down

# 重新构建
docker-compose build --no-cache
```

## 🔧 运维管理

### 日志管理
```python
# 日志配置示例
import logging
from loguru import logger

# 配置日志格式
logger.add(
    "logs/hyaiagent_{time:YYYY-MM-DD}.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)
```

### 监控指标
- **系统指标**: CPU使用率、内存使用率、磁盘空间
- **应用指标**: 任务执行数量、AI API调用次数、错误率
- **性能指标**: 响应时间、吞吐量、队列长度

### 备份策略
```bash
#!/bin/bash
# 数据备份脚本

# 备份数据库
pg_dump hyaiagent > backups/db_backup_$(date +%Y%m%d_%H%M%S).sql

# 备份配置文件
tar -czf backups/config_backup_$(date +%Y%m%d_%H%M%S).tar.gz config/

# 备份工作空间
tar -czf backups/workspace_backup_$(date +%Y%m%d_%H%M%S).tar.gz workspace/

# 清理旧备份（保留30天）
find backups/ -name "*.sql" -mtime +30 -delete
find backups/ -name "*.tar.gz" -mtime +30 -delete
```

## 🔒 安全配置

### API密钥管理
- 使用环境变量存储敏感信息
- 定期轮换API密钥
- 实施访问控制和审计

### 网络安全
- 启用HTTPS
- 配置防火墙规则
- 实施速率限制

### 数据安全
- 数据库连接加密
- 敏感数据加密存储
- 定期安全扫描

---

**📝 说明**: 这个配置和部署指南提供了HyAIAgent框架的完整部署流程，包括环境配置、Docker部署、运维管理等关键环节。
