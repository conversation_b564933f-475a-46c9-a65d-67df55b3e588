# 🔧 HyAIAgent 技术实现指南

## 📋 技术栈选择

### 核心技术
- **Python 3.9+** - 主要开发语言
- **PyQt6** - 桌面GUI框架
- **SQLite** - 轻量级数据库
- **asyncio** - 异步编程支持
- **OpenAI API** - AI模型接口
- **Anthropic API** - Claude模型接口
- **Tavily API** - 网络搜索服务

### 依赖库
```txt
# 核心框架
asyncio>=3.4.3
aiohttp>=3.8.0
aiofiles>=0.8.0

# AI客户端
openai>=1.0.0
anthropic>=0.7.0

# 数据库
aiosqlite>=0.18.0

# 网络搜索
tavily-python>=0.3.0

# GUI界面
PyQt6>=6.4.0

# 工具库
python-dotenv>=1.0.0
loguru>=0.6.0
pydantic>=1.10.0
```

## 🏗️ 项目结构设计

```
HyAIAgent/
├── main.py                        # 程序入口
├── config.json                    # 主配置文件
├── .env                           # 环境变量
├── requirements.txt               # 依赖包
├── README.md                      # 项目说明
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── agent.py                   # 主控制器
│   ├── ai_client.py              # AI客户端管理
│   ├── task_manager.py           # 任务管理
│   ├── database.py               # 数据库操作
│   └── config_manager.py         # 配置管理
├── operations/                    # 操作模块
│   ├── __init__.py
│   ├── base_operation.py         # 操作基类
│   ├── file_operations.py        # 文件操作
│   ├── db_operations.py          # 数据库操作
│   ├── search_operations.py      # 搜索操作
│   └── cache_operations.py       # 缓存操作
├── ui/                           # 用户界面
│   ├── __init__.py
│   ├── main_window.py           # 主窗口
│   ├── chat_widget.py           # 聊天组件
│   ├── task_monitor.py          # 任务监控
│   └── settings_dialog.py       # 设置对话框
├── utils/                        # 工具类
│   ├── __init__.py
│   ├── logger.py                # 日志工具
│   ├── helpers.py               # 辅助函数
│   └── validators.py            # 验证工具
├── data/                         # 数据目录
│   ├── hyaiagent.db             # SQLite数据库
│   └── cache/                   # 缓存文件
├── logs/                         # 日志目录
├── workspace/                    # 工作目录
└── tests/                        # 测试代码
    ├── __init__.py
    ├── test_ai_client.py
    ├── test_operations.py
    └── test_database.py
```

## 🤖 AI客户端实现

### 多AI模型支持
```python
import asyncio
import openai
import anthropic
from typing import Dict, Any, Optional
from enum import Enum

class AIProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

class AIClientManager:
    """多AI模型客户端管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.default_provider = AIProvider.OPENAI
        self._init_clients()
    
    def _init_clients(self):
        """初始化AI客户端"""
        # OpenAI客户端
        if self.config.get('openai', {}).get('api_key'):
            self.clients[AIProvider.OPENAI] = openai.AsyncOpenAI(
                api_key=self.config['openai']['api_key']
            )
        
        # Anthropic客户端
        if self.config.get('anthropic', {}).get('api_key'):
            self.clients[AIProvider.ANTHROPIC] = anthropic.AsyncAnthropic(
                api_key=self.config['anthropic']['api_key']
            )
    
    async def chat_completion(self, 
                            messages: list, 
                            provider: Optional[AIProvider] = None,
                            **kwargs) -> str:
        """统一的聊天完成接口"""
        provider = provider or self.default_provider
        
        if provider not in self.clients:
            raise ValueError(f"AI provider {provider.value} not configured")
        
        if provider == AIProvider.OPENAI:
            return await self._openai_completion(messages, **kwargs)
        elif provider == AIProvider.ANTHROPIC:
            return await self._anthropic_completion(messages, **kwargs)
    
    async def _openai_completion(self, messages: list, **kwargs) -> str:
        """OpenAI聊天完成"""
        client = self.clients[AIProvider.OPENAI]
        
        response = await client.chat.completions.create(
            model=kwargs.get('model', 'gpt-4'),
            messages=messages,
            max_tokens=kwargs.get('max_tokens', 4000),
            temperature=kwargs.get('temperature', 0.7)
        )
        
        return response.choices[0].message.content
    
    async def _anthropic_completion(self, messages: list, **kwargs) -> str:
        """Anthropic聊天完成"""
        client = self.clients[AIProvider.ANTHROPIC]
        
        # 转换消息格式
        system_message = ""
        user_messages = []
        
        for msg in messages:
            if msg['role'] == 'system':
                system_message = msg['content']
            else:
                user_messages.append(msg)
        
        response = await client.messages.create(
            model=kwargs.get('model', 'claude-3-sonnet-20240229'),
            max_tokens=kwargs.get('max_tokens', 4000),
            temperature=kwargs.get('temperature', 0.7),
            system=system_message,
            messages=user_messages
        )
        
        return response.content[0].text
```

## 📋 任务管理系统

### 任务管理器
```python
import asyncio
import uuid
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskManager:
    """任务管理器"""
    
    def __init__(self, database, operation_manager):
        self.database = database
        self.operation_manager = operation_manager
        self.task_queue = asyncio.Queue()
        self.running_tasks = {}
        self.max_concurrent_tasks = 3
        
    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        
        task = {
            'task_id': task_id,
            'type': task_data.get('type', 'unknown'),
            'description': task_data.get('description', ''),
            'parameters': task_data.get('parameters', {}),
            'priority': task_data.get('priority', 5),
            'dependencies': task_data.get('dependencies', []),
            'status': TaskStatus.PENDING.value,
            'created_at': datetime.now().isoformat()
        }
        
        # 保存到数据库
        await self.database.create_task(task)
        
        # 添加到队列
        await self.task_queue.put(task_id)
        
        return task_id
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """执行任务"""
        try:
            # 获取任务信息
            task = await self.database.get_task(task_id)
            if not task:
                return {'status': 'error', 'message': 'Task not found'}
            
            # 更新任务状态
            await self.database.update_task_status(task_id, TaskStatus.RUNNING.value)
            
            # 执行操作
            operation_type = task['type']
            parameters = json.loads(task['parameters']) if isinstance(task['parameters'], str) else task['parameters']
            
            result = await self.operation_manager.execute_operation(
                operation_type, 
                parameters
            )
            
            # 更新任务结果
            await self.database.update_task_result(
                task_id, 
                TaskStatus.COMPLETED.value,
                result
            )
            
            return result
            
        except Exception as e:
            # 更新任务状态为失败
            await self.database.update_task_status(task_id, TaskStatus.FAILED.value)
            return {'status': 'error', 'message': str(e)}
    
    async def start_task_processor(self):
        """启动任务处理器"""
        while True:
            try:
                # 检查并发任务数量
                if len(self.running_tasks) >= self.max_concurrent_tasks:
                    await asyncio.sleep(1)
                    continue
                
                # 获取下一个任务
                task_id = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                # 创建任务协程
                task_coroutine = self.execute_task(task_id)
                task = asyncio.create_task(task_coroutine)
                
                # 添加到运行任务列表
                self.running_tasks[task_id] = task
                
                # 设置任务完成回调
                task.add_done_callback(lambda t, tid=task_id: self.running_tasks.pop(tid, None))
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"Task processor error: {e}")
                await asyncio.sleep(1)
```

## 🔧 操作模块实现

### 操作基类
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List

class BaseOperation(ABC):
    """操作模块基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @abstractmethod
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行操作"""
        pass
    
    @abstractmethod
    def get_supported_operations(self) -> List[str]:
        """获取支持的操作列表"""
        pass
    
    def validate_parameters(self, operation: str, parameters: Dict[str, Any]) -> bool:
        """验证参数"""
        return True
```

### 文件操作模块
```python
import os
import aiofiles
from pathlib import Path
from typing import Dict, Any, List

class FileOperations(BaseOperation):
    """文件操作模块"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.workspace = Path(config.get('workspace', './workspace'))
        self.workspace.mkdir(exist_ok=True)
        self.allowed_extensions = config.get('allowed_extensions', ['.txt', '.md', '.json'])
        self.max_file_size = config.get('max_file_size', 10 * 1024 * 1024)  # 10MB
    
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件操作"""
        if operation == 'read_file':
            return await self.read_file(parameters.get('file_path'))
        elif operation == 'write_file':
            return await self.write_file(
                parameters.get('file_path'),
                parameters.get('content', '')
            )
        elif operation == 'delete_file':
            return await self.delete_file(parameters.get('file_path'))
        elif operation == 'list_files':
            return await self.list_files(parameters.get('directory', '.'))
        elif operation == 'file_exists':
            return await self.file_exists(parameters.get('file_path'))
        else:
            return {'status': 'error', 'message': f'Unsupported operation: {operation}'}
    
    async def read_file(self, file_path: str) -> Dict[str, Any]:
        """读取文件"""
        try:
            full_path = self.workspace / file_path
            
            # 安全检查
            if not self._is_safe_path(full_path):
                return {'status': 'error', 'message': '文件路径不安全'}
            
            if not full_path.exists():
                return {'status': 'error', 'message': '文件不存在'}
            
            # 检查文件大小
            if full_path.stat().st_size > self.max_file_size:
                return {'status': 'error', 'message': '文件过大'}
            
            async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            return {
                'status': 'success',
                'content': content,
                'file_path': file_path,
                'file_size': full_path.stat().st_size
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    async def write_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """写入文件"""
        try:
            full_path = self.workspace / file_path
            
            # 安全检查
            if not self._is_safe_path(full_path):
                return {'status': 'error', 'message': '文件路径不安全'}
            
            # 检查文件扩展名
            if full_path.suffix not in self.allowed_extensions:
                return {'status': 'error', 'message': '不支持的文件类型'}
            
            # 创建目录
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(full_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            return {
                'status': 'success',
                'message': f'文件 {file_path} 写入成功',
                'file_path': file_path,
                'bytes_written': len(content.encode('utf-8'))
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    async def delete_file(self, file_path: str) -> Dict[str, Any]:
        """删除文件"""
        try:
            full_path = self.workspace / file_path
            
            # 安全检查
            if not self._is_safe_path(full_path):
                return {'status': 'error', 'message': '文件路径不安全'}
            
            if not full_path.exists():
                return {'status': 'error', 'message': '文件不存在'}
            
            full_path.unlink()
            
            return {
                'status': 'success',
                'message': f'文件 {file_path} 删除成功',
                'file_path': file_path
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    async def list_files(self, directory: str = '.') -> Dict[str, Any]:
        """列出文件"""
        try:
            full_path = self.workspace / directory
            
            # 安全检查
            if not self._is_safe_path(full_path):
                return {'status': 'error', 'message': '目录路径不安全'}
            
            if not full_path.exists():
                return {'status': 'error', 'message': '目录不存在'}
            
            files = []
            for item in full_path.iterdir():
                files.append({
                    'name': item.name,
                    'type': 'directory' if item.is_dir() else 'file',
                    'size': item.stat().st_size if item.is_file() else 0,
                    'modified': item.stat().st_mtime
                })
            
            return {
                'status': 'success',
                'directory': directory,
                'files': files,
                'total_count': len(files)
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    async def file_exists(self, file_path: str) -> Dict[str, Any]:
        """检查文件是否存在"""
        try:
            full_path = self.workspace / file_path
            
            # 安全检查
            if not self._is_safe_path(full_path):
                return {'status': 'error', 'message': '文件路径不安全'}
            
            exists = full_path.exists()
            
            return {
                'status': 'success',
                'file_path': file_path,
                'exists': exists
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _is_safe_path(self, path: Path) -> bool:
        """检查路径安全性"""
        try:
            path.resolve().relative_to(self.workspace.resolve())
            return True
        except ValueError:
            return False
    
    def get_supported_operations(self) -> List[str]:
        """获取支持的操作列表"""
        return ['read_file', 'write_file', 'delete_file', 'list_files', 'file_exists']
```

## 💾 数据库实现

### SQLite数据库管理
```python
import aiosqlite
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional

class HyAIDatabase:
    """HyAIAgent SQLite数据库管理"""
    
    def __init__(self, db_path: str = "data/hyaiagent.db"):
        self.db_path = db_path
        self.connection = None
    
    async def init_database(self):
        """初始化数据库"""
        # 确保数据目录存在
        import os
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        self.connection = await aiosqlite.connect(self.db_path)
        await self._create_tables()
    
    async def _create_tables(self):
        """创建数据库表"""
        # 任务表
        await self.connection.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT UNIQUE NOT NULL,
                type TEXT NOT NULL,
                description TEXT,
                parameters TEXT,
                status TEXT DEFAULT 'pending',
                priority INTEGER DEFAULT 5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                result TEXT
            )
        ''')
        
        # 对话历史表
        await self.connection.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ai_provider TEXT,
                tokens_used INTEGER
            )
        ''')
        
        # AI缓存表
        await self.connection.execute('''
            CREATE TABLE IF NOT EXISTS ai_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cache_key TEXT UNIQUE NOT NULL,
                content TEXT NOT NULL,
                tags TEXT,
                importance REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                use_count INTEGER DEFAULT 0
            )
        ''')
        
        await self.connection.commit()
    
    # 任务相关操作
    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        await self.connection.execute(
            '''INSERT INTO tasks (task_id, type, description, parameters, priority)
               VALUES (?, ?, ?, ?, ?)''',
            (task_data['task_id'], task_data['type'], task_data['description'],
             json.dumps(task_data.get('parameters', {})), task_data.get('priority', 5))
        )
        await self.connection.commit()
        return task_data['task_id']
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        async with self.connection.execute(
            "SELECT * FROM tasks WHERE task_id = ?", (task_id,)
        ) as cursor:
            row = await cursor.fetchone()
            if row:
                return self._row_to_dict(row, cursor.description)
            return None
    
    async def update_task_status(self, task_id: str, status: str):
        """更新任务状态"""
        await self.connection.execute(
            "UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE task_id = ?",
            (status, task_id)
        )
        await self.connection.commit()
    
    async def update_task_result(self, task_id: str, status: str, result: Dict[str, Any]):
        """更新任务结果"""
        await self.connection.execute(
            '''UPDATE tasks SET status = ?, result = ?, completed_at = CURRENT_TIMESTAMP, 
               updated_at = CURRENT_TIMESTAMP WHERE task_id = ?''',
            (status, json.dumps(result), task_id)
        )
        await self.connection.commit()
    
    def _row_to_dict(self, row, description) -> Dict[str, Any]:
        """将数据库行转换为字典"""
        return {desc[0]: row[i] for i, desc in enumerate(description)}
    
    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            await self.connection.close()
```

---

**🎯 实现目标**: 提供详细的技术实现指导，确保开发团队能够按照统一的标准和最佳实践进行开发。
