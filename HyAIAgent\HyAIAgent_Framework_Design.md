# 🤖 HyAIAgent 自主AI助手框架设计文档

## 📋 项目概述

HyAIAgent是一个自主思考、自主决策的AI助手框架，能够根据用户输入自动拆分任务、调用AI API、执行本地操作，并持续迭代优化直到完成目标。

### 🎯 V1版本核心特性
- **自主任务分解** - AI自动将复杂任务拆分为可执行的子任务
- **多AI模型支持** - 支持OpenAI、Anthropic等多种AI模型
- **基础操作能力** - 支持文件操作、数据库操作、网络搜索(Tavily)
- **轻量级存储** - 基于SQLite的本地数据存储
- **简洁架构** - 专注核心功能，避免过度设计

### 🚫 V1版本不包含的功能
- Office文档处理（预留扩展接口）
- 自动化脚本执行
- 插件系统和生态建设
- Web界面和移动适配
- 主题系统和国际化
- 自适应学习和知识图谱
- 智能调度（由AI端点决定）

## 🏗️ 系统架构设计

### V1版本精简架构
```
┌─────────────────────────────────────────────────────────────┐
│                 HyAIAgent V1 Framework                      │
├─────────────────────────────────────────────────────────────┤
│  🧠 AI Client Manager (AI客户端管理)                        │
│  ├── Multi-AI Support (多AI模型支持)                        │
│  ├── OpenAI Client (OpenAI客户端)                           │
│  ├── Anthropic Client (Anthropic客户端)                     │
│  └── Request Router (请求路由器)                            │
├─────────────────────────────────────────────────────────────┤
│  📋 Task Management (任务管理)                              │
│  ├── Task Queue (任务队列)                                  │
│  ├── Task Executor (任务执行器)                             │
│  └── Progress Monitor (进度监控)                            │
├─────────────────────────────────────────────────────────────┤
│  🔧 Core Operations (核心操作)                              │
│  ├── File Operations (文件操作)                             │
│  ├── Database Operations (SQLite操作)                       │
│  ├── Tavily Search (Tavily网络搜索)                         │
│  └── Cache Operations (缓存操作)                            │
├─────────────────────────────────────────────────────────────┤
│  💾 SQLite Storage (SQLite存储)                             │
│  ├── Task Records (任务记录)                                │
│  ├── Conversation History (对话历史)                        │
│  ├── Cache Data (缓存数据)                                  │
│  └── Configuration (配置信息)                               │
├─────────────────────────────────────────────────────────────┤
│  🌐 Communication (通信层)                                  │
│  ├── JSON Protocol (JSON协议)                               │
│  ├── Response Parser (响应解析)                             │
│  └── Error Handler (错误处理)                               │
└─────────────────────────────────────────────────────────────┘
```

## 📡 通信协议设计

### 🔄 AI交互JSON协议

#### 发送给AI的信息结构
```json
{
  "request_id": "uuid",
  "timestamp": "2024-01-01T12:00:00Z",
  "user_input": {
    "type": "user_query|task_update|feedback",
    "content": "用户输入内容",
    "context": "上下文信息"
  },
  "system_context": {
    "current_tasks": [
      {
        "task_id": "uuid",
        "type": "file_edit|db_operation|ai_analysis|web_search",
        "status": "pending|running|completed|failed",
        "priority": 1-10,
        "description": "任务描述",
        "dependencies": ["task_id1", "task_id2"],
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    ],
    "ai_cache": {
      "knowledge_base": [
        {
          "key": "知识点标识",
          "content": "知识内容",
          "relevance_score": 0.95,
          "last_used": "timestamp"
        }
      ],
      "conversation_history": [
        {
          "role": "user|assistant",
          "content": "对话内容",
          "timestamp": "timestamp"
        }
      ]
    },
    "database_context": {
      "recent_queries": ["SQL查询历史"],
      "table_schemas": ["表结构信息"],
      "data_summary": "数据概要"
    },
    "file_context": {
      "current_directory": "/path/to/current",
      "recent_files": ["文件列表"],
      "file_changes": ["最近修改的文件"]
    },
    "search_context": {
      "recent_searches": ["搜索历史"],
      "search_results_cache": ["缓存的搜索结果"]
    }
  },
  "capabilities": {
    "available_operations": [
      "file_read", "file_write", "file_delete",
      "db_query", "db_update", "db_insert",
      "web_search", "ai_cache_update",
      "task_create", "task_update"
    ],
    "constraints": {
      "max_file_size": "10MB",
      "allowed_file_types": [".txt", ".md", ".json", ".py"],
      "db_permissions": ["read", "write"],
      "api_rate_limits": {
        "requests_per_minute": 60,
        "tokens_per_request": 4000
      }
    }
  }
}
```

#### AI返回的信息结构
```json
{
  "response_id": "uuid",
  "request_id": "对应的请求ID",
  "timestamp": "2024-01-01T12:00:00Z",
  "analysis": {
    "understanding": "对用户输入的理解",
    "complexity_assessment": "simple|medium|complex",
    "estimated_steps": 5,
    "confidence_level": 0.85
  },
  "user_response": {
    "type": "answer|clarification|progress_update",
    "content": "给用户的回复内容",
    "suggestions": ["建议1", "建议2"]
  },
  "task_operations": {
    "create_tasks": [
      {
        "task_id": "uuid",
        "type": "file_edit|db_operation|ai_analysis|web_search",
        "priority": 1-10,
        "description": "任务描述",
        "parameters": {
          "file_path": "/path/to/file",
          "operation": "read|write|delete",
          "content": "文件内容",
          "query": "数据库查询",
          "search_terms": "搜索关键词"
        },
        "dependencies": ["task_id1"],
        "expected_duration": "5min",
        "success_criteria": "成功标准"
      }
    ],
    "update_tasks": [
      {
        "task_id": "uuid",
        "status": "completed|failed|paused",
        "progress": 0.75,
        "notes": "更新说明"
      }
    ],
    "delete_tasks": ["task_id1", "task_id2"]
  },
  "ai_cache_operations": {
    "add_knowledge": [
      {
        "key": "知识点标识",
        "content": "知识内容",
        "tags": ["标签1", "标签2"],
        "importance": 0.8
      }
    ],
    "update_knowledge": [
      {
        "key": "知识点标识",
        "content": "更新的内容",
        "importance": 0.9
      }
    ],
    "delete_knowledge": ["key1", "key2"]
  },
  "operation_commands": [
    {
      "command_id": "uuid",
      "type": "file_operation|db_operation|web_search",
      "operation": "具体操作类型",
      "parameters": {
        "target": "操作目标",
        "action": "具体动作",
        "data": "操作数据"
      },
      "execute_immediately": true,
      "depends_on": ["command_id1"]
    }
  ],
  "next_action": {
    "type": "wait_for_user|continue_processing|request_clarification",
    "message": "下一步行动说明",
    "estimated_time": "预估时间"
  }
}
```

## 🔧 核心模块详细设计

### 1. AI决策引擎 (AI Decision Engine)

#### 任务分析器 (Task Analyzer)
- **功能**: 分析用户输入，识别任务类型和复杂度
- **输入**: 用户查询、上下文信息
- **输出**: 任务分类、复杂度评估、所需资源

#### 策略选择器 (Strategy Selector)
- **功能**: 根据任务特征选择最优执行策略
- **策略类型**:
  - 顺序执行策略
  - 并行执行策略
  - 迭代优化策略
  - 分治策略

#### 执行规划器 (Execution Planner)
- **功能**: 制定详细的执行计划和时间表
- **输出**: 任务依赖图、执行顺序、资源分配

### 2. 任务管理系统 (Task Management System)

#### 任务队列 (Task Queue)
```python
class TaskQueue:
    def __init__(self):
        self.pending_tasks = []
        self.running_tasks = []
        self.completed_tasks = []
        self.failed_tasks = []
    
    def add_task(self, task):
        """添加新任务到队列"""
        pass
    
    def get_next_task(self):
        """获取下一个待执行任务"""
        pass
    
    def update_task_status(self, task_id, status):
        """更新任务状态"""
        pass
```

#### 任务执行器 (Task Executor)
```python
class TaskExecutor:
    def __init__(self):
        self.operation_modules = {}
        self.max_concurrent_tasks = 5
    
    def execute_task(self, task):
        """执行单个任务"""
        pass
    
    def execute_batch(self, tasks):
        """批量执行任务"""
        pass
```

### 3. 操作模块 (Operation Modules)

#### 文件操作模块
```python
class FileOperations:
    def read_file(self, file_path):
        """读取文件内容"""
        pass
    
    def write_file(self, file_path, content):
        """写入文件内容"""
        pass
    
    def delete_file(self, file_path):
        """删除文件"""
        pass
    
    def edit_file(self, file_path, edits):
        """编辑文件（支持Word/PPT/Excel）"""
        pass
```

#### 数据库操作模块
```python
class DatabaseOperations:
    def __init__(self, db_config):
        self.connection = None
        self.config = db_config
    
    def execute_query(self, query):
        """执行查询"""
        pass
    
    def execute_update(self, query, params):
        """执行更新操作"""
        pass
    
    def get_schema(self, table_name):
        """获取表结构"""
        pass
```

#### AI缓存操作模块
```python
class AICacheOperations:
    def __init__(self):
        self.knowledge_base = {}
        self.conversation_history = []
    
    def add_knowledge(self, key, content, tags=None):
        """添加知识到缓存"""
        pass
    
    def search_knowledge(self, query):
        """搜索相关知识"""
        pass
    
    def update_conversation(self, role, content):
        """更新对话历史"""
        pass
```

## 🚀 实施计划

### 第一阶段：核心框架搭建 (2-3周)
1. 设计基础架构和接口
2. 实现JSON通信协议
3. 开发任务管理系统
4. 创建基础操作模块

### 第二阶段：AI集成和测试 (2-3周)
1. 集成AI API接口
2. 实现AI决策引擎
3. 开发AI缓存系统
4. 进行基础功能测试

### 第三阶段：功能扩展和优化 (3-4周)
1. 扩展文件操作支持（Office文档）
2. 完善数据库操作功能
3. 添加网络搜索能力
4. 性能优化和错误处理

### 第四阶段：用户界面和部署 (2-3周)
1. 开发用户交互界面
2. 完善日志和监控系统
3. 编写文档和使用指南
4. 部署和发布准备

## 🔍 技术栈选择

### 后端框架
- **Python 3.9+** - 主要开发语言
- **FastAPI** - API服务框架
- **SQLAlchemy** - 数据库ORM
- **Redis** - 缓存和任务队列
- **Celery** - 异步任务处理

### 前端界面
- **PyQt6** - 桌面应用界面
- **Vue.js** - Web管理界面（可选）

### 数据存储
- **SQLite/PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **JSON文件** - 配置和临时数据

### AI集成
- **OpenAI API** - GPT模型接口
- **Anthropic Claude** - 备选AI模型
- **本地模型** - 离线AI能力（可选）

## 📊 扩展性考虑

### 插件系统
- 支持第三方插件开发
- 标准化插件接口
- 动态加载和卸载插件

### 多AI模型支持
- 抽象AI接口层
- 支持多种AI模型切换
- 模型性能对比和选择

### 分布式部署
- 微服务架构支持
- 负载均衡和容错
- 水平扩展能力

### 安全性增强
- 用户权限管理
- 操作审计日志
- 数据加密和隐私保护

## 💡 智能提示词设计

### AI助手系统提示词模板
```
你是HyAIAgent框架中的智能决策引擎。你的任务是：

1. **理解用户意图**: 深度分析用户输入，识别真实需求和隐含目标
2. **任务分解**: 将复杂任务拆分为可执行的原子操作
3. **策略制定**: 选择最优的执行策略和操作顺序
4. **资源调度**: 合理分配系统资源和操作权限
5. **持续优化**: 根据执行结果调整策略和改进方案

## 输入信息结构:
- user_input: 用户的原始输入
- system_context: 当前系统状态和上下文
- ai_cache: 历史知识和对话记录
- capabilities: 可用的操作能力和约束条件

## 输出要求:
请严格按照JSON格式返回，包含以下部分：
1. analysis: 对任务的分析和理解
2. user_response: 给用户的回复
3. task_operations: 任务创建、更新、删除操作
4. ai_cache_operations: 知识缓存的增删改操作
5. operation_commands: 具体的执行命令
6. next_action: 下一步行动计划

## 操作指令规范:

### 文件操作指令
{
  "type": "file_operation",
  "operation": "read|write|delete|edit",
  "parameters": {
    "file_path": "文件路径",
    "content": "文件内容（写入时）",
    "encoding": "utf-8",
    "backup": true
  }
}

### 数据库操作指令
{
  "type": "db_operation",
  "operation": "query|insert|update|delete",
  "parameters": {
    "query": "SQL语句",
    "params": ["参数列表"],
    "table": "表名",
    "transaction": true
  }
}

### 网络搜索指令
{
  "type": "web_search",
  "operation": "search|scrape",
  "parameters": {
    "query": "搜索关键词",
    "max_results": 10,
    "source": "google|bing|custom"
  }
}

### AI缓存操作指令
{
  "type": "ai_cache_operation",
  "operation": "add|update|delete|search",
  "parameters": {
    "key": "知识点标识",
    "content": "知识内容",
    "tags": ["标签"],
    "importance": 0.8
  }
}

请始终保持理性、高效、安全的决策原则。
```

## 🔧 核心代码实现示例

### 主控制器类
```python
import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

class HyAIAgentController:
    """HyAIAgent主控制器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.task_manager = TaskManager()
        self.ai_client = AIClient(config['ai_api'])
        self.operation_modules = self._init_operation_modules()
        self.ai_cache = AICacheManager()
        self.is_running = False

    def _init_operation_modules(self) -> Dict[str, Any]:
        """初始化操作模块"""
        return {
            'file': FileOperations(),
            'database': DatabaseOperations(self.config['database']),
            'web_search': WebSearchOperations(),
            'ai_cache': self.ai_cache
        }

    async def process_user_input(self, user_input: str) -> Dict[str, Any]:
        """处理用户输入的主要方法"""
        try:
            # 1. 构建发送给AI的上下文信息
            context = await self._build_context(user_input)

            # 2. 调用AI进行分析和决策
            ai_response = await self.ai_client.analyze_and_decide(context)

            # 3. 解析AI响应
            parsed_response = self._parse_ai_response(ai_response)

            # 4. 执行AI指定的操作
            execution_results = await self._execute_operations(parsed_response)

            # 5. 更新系统状态
            await self._update_system_state(parsed_response, execution_results)

            # 6. 返回用户响应
            return {
                'user_response': parsed_response.get('user_response', {}),
                'execution_results': execution_results,
                'next_action': parsed_response.get('next_action', {})
            }

        except Exception as e:
            return self._handle_error(e)

    async def _build_context(self, user_input: str) -> Dict[str, Any]:
        """构建发送给AI的完整上下文"""
        return {
            'request_id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'user_input': {
                'type': 'user_query',
                'content': user_input,
                'context': 'direct_input'
            },
            'system_context': {
                'current_tasks': await self.task_manager.get_active_tasks(),
                'ai_cache': await self.ai_cache.get_relevant_knowledge(user_input),
                'database_context': await self._get_database_context(),
                'file_context': await self._get_file_context(),
                'search_context': await self._get_search_context()
            },
            'capabilities': self._get_capabilities()
        }

    async def _execute_operations(self, ai_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行AI指定的操作"""
        results = []

        # 执行任务操作
        if 'task_operations' in ai_response:
            task_results = await self._execute_task_operations(
                ai_response['task_operations']
            )
            results.extend(task_results)

        # 执行AI缓存操作
        if 'ai_cache_operations' in ai_response:
            cache_results = await self._execute_cache_operations(
                ai_response['ai_cache_operations']
            )
            results.extend(cache_results)

        # 执行具体操作命令
        if 'operation_commands' in ai_response:
            command_results = await self._execute_commands(
                ai_response['operation_commands']
            )
            results.extend(command_results)

        return results

    async def _execute_commands(self, commands: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行具体的操作命令"""
        results = []

        for command in commands:
            try:
                command_type = command.get('type')
                operation = command.get('operation')
                parameters = command.get('parameters', {})

                if command_type in self.operation_modules:
                    module = self.operation_modules[command_type]
                    result = await module.execute(operation, parameters)
                    results.append({
                        'command_id': command.get('command_id'),
                        'status': 'success',
                        'result': result
                    })
                else:
                    results.append({
                        'command_id': command.get('command_id'),
                        'status': 'error',
                        'error': f'Unknown command type: {command_type}'
                    })

            except Exception as e:
                results.append({
                    'command_id': command.get('command_id'),
                    'status': 'error',
                    'error': str(e)
                })

        return results
```

### 任务管理器
```python
class TaskManager:
    """任务管理器"""

    def __init__(self):
        self.tasks = {}
        self.task_queue = asyncio.Queue()
        self.running_tasks = set()
        self.max_concurrent = 5

    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        task = Task(
            task_id=task_id,
            task_type=task_data.get('type'),
            description=task_data.get('description'),
            parameters=task_data.get('parameters', {}),
            priority=task_data.get('priority', 5),
            dependencies=task_data.get('dependencies', [])
        )

        self.tasks[task_id] = task
        await self.task_queue.put(task)

        return task_id

    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取当前活跃任务"""
        active_tasks = []
        for task in self.tasks.values():
            if task.status in ['pending', 'running']:
                active_tasks.append(task.to_dict())
        return active_tasks

    async def update_task_status(self, task_id: str, status: str, result: Any = None):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id].status = status
            self.tasks[task_id].updated_at = datetime.now()
            if result:
                self.tasks[task_id].result = result

class Task:
    """任务类"""

    def __init__(self, task_id: str, task_type: str, description: str,
                 parameters: Dict[str, Any], priority: int = 5,
                 dependencies: List[str] = None):
        self.task_id = task_id
        self.task_type = task_type
        self.description = description
        self.parameters = parameters
        self.priority = priority
        self.dependencies = dependencies or []
        self.status = 'pending'
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.result = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'type': self.task_type,
            'description': self.description,
            'parameters': self.parameters,
            'priority': self.priority,
            'dependencies': self.dependencies,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'result': self.result
        }
```

---

**🎯 项目目标**: 构建一个智能、自主、可扩展的AI助手框架，为用户提供强大的自动化能力和智能决策支持。

**🚀 核心价值**: 通过AI的自主思考和决策能力，大幅提升工作效率，减少重复性操作，实现真正的智能助手体验。
