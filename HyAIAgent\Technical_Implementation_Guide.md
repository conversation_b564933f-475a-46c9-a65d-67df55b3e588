# 🔧 HyAIAgent 技术实现指南

## 📁 项目结构设计

```
HyAIAgent/
├── core/                          # 核心框架
│   ├── __init__.py
│   ├── agent_controller.py        # 主控制器
│   ├── ai_client.py              # AI API客户端
│   ├── task_manager.py           # 任务管理器
│   ├── decision_engine.py        # 决策引擎
│   └── communication/            # 通信层
│       ├── __init__.py
│       ├── json_protocol.py      # JSON协议处理
│       └── response_parser.py    # 响应解析器
├── operations/                    # 操作模块
│   ├── __init__.py
│   ├── base_operation.py         # 基础操作类
│   ├── file_operations.py        # 文件操作
│   ├── database_operations.py    # 数据库操作
│   ├── web_search_operations.py  # 网络搜索
│   └── ai_cache_operations.py    # AI缓存操作
├── data/                          # 数据管理
│   ├── __init__.py
│   ├── models.py                 # 数据模型
│   ├── database.py               # 数据库连接
│   ├── cache_manager.py          # 缓存管理
│   └── config_manager.py         # 配置管理
├── ui/                           # 用户界面
│   ├── __init__.py
│   ├── main_window.py           # 主窗口
│   ├── chat_interface.py        # 聊天界面
│   └── task_monitor.py          # 任务监控界面
├── utils/                        # 工具类
│   ├── __init__.py
│   ├── logger.py                # 日志工具
│   ├── validators.py            # 验证工具
│   └── helpers.py               # 辅助函数
├── plugins/                      # 插件系统
│   ├── __init__.py
│   ├── plugin_manager.py        # 插件管理器
│   └── examples/                # 示例插件
├── tests/                        # 测试代码
│   ├── __init__.py
│   ├── test_core/
│   ├── test_operations/
│   └── test_integration/
├── config/                       # 配置文件
│   ├── default_config.json
│   ├── ai_prompts.json
│   └── operation_schemas.json
├── docs/                         # 文档
│   ├── api_reference.md
│   ├── user_guide.md
│   └── plugin_development.md
├── requirements.txt              # 依赖包
├── setup.py                     # 安装脚本
└── main.py                      # 程序入口
```

## 🔌 操作模块实现

### 基础操作类
```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import asyncio
import logging

class BaseOperation(ABC):
    """所有操作模块的基类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @abstractmethod
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行操作的抽象方法"""
        pass
    
    @abstractmethod
    def get_supported_operations(self) -> List[str]:
        """获取支持的操作列表"""
        pass
    
    def validate_parameters(self, operation: str, parameters: Dict[str, Any]) -> bool:
        """验证参数的有效性"""
        return True
    
    async def _log_operation(self, operation: str, parameters: Dict[str, Any], result: Any):
        """记录操作日志"""
        self.logger.info(f"Operation: {operation}, Parameters: {parameters}, Result: {result}")
```

### 文件操作模块
```python
import os
import aiofiles
import json
from pathlib import Path
from typing import Dict, Any, List
import mimetypes

class FileOperations(BaseOperation):
    """文件操作模块"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.allowed_extensions = config.get('allowed_extensions', [
            '.txt', '.md', '.json', '.py', '.js', '.html', '.css', '.xml'
        ])
        self.max_file_size = config.get('max_file_size', 10 * 1024 * 1024)  # 10MB
        self.base_path = Path(config.get('base_path', '.'))
        
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件操作"""
        if not self.validate_parameters(operation, parameters):
            return {'status': 'error', 'message': 'Invalid parameters'}
        
        try:
            if operation == 'read':
                return await self._read_file(parameters)
            elif operation == 'write':
                return await self._write_file(parameters)
            elif operation == 'delete':
                return await self._delete_file(parameters)
            elif operation == 'list':
                return await self._list_files(parameters)
            elif operation == 'exists':
                return await self._file_exists(parameters)
            elif operation == 'info':
                return await self._get_file_info(parameters)
            else:
                return {'status': 'error', 'message': f'Unsupported operation: {operation}'}
                
        except Exception as e:
            self.logger.error(f"File operation error: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def get_supported_operations(self) -> List[str]:
        return ['read', 'write', 'delete', 'list', 'exists', 'info']
    
    async def _read_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """读取文件内容"""
        file_path = self._resolve_path(parameters['file_path'])
        encoding = parameters.get('encoding', 'utf-8')
        
        if not file_path.exists():
            return {'status': 'error', 'message': 'File not found'}
        
        if file_path.stat().st_size > self.max_file_size:
            return {'status': 'error', 'message': 'File too large'}
        
        async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
            content = await f.read()
        
        return {
            'status': 'success',
            'content': content,
            'file_path': str(file_path),
            'size': file_path.stat().st_size,
            'encoding': encoding
        }
    
    async def _write_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """写入文件内容"""
        file_path = self._resolve_path(parameters['file_path'])
        content = parameters['content']
        encoding = parameters.get('encoding', 'utf-8')
        backup = parameters.get('backup', False)
        
        # 创建目录（如果不存在）
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 备份原文件
        if backup and file_path.exists():
            backup_path = file_path.with_suffix(file_path.suffix + '.backup')
            await aiofiles.os.rename(file_path, backup_path)
        
        async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
            await f.write(content)
        
        return {
            'status': 'success',
            'file_path': str(file_path),
            'size': len(content.encode(encoding)),
            'backup_created': backup and file_path.exists()
        }
    
    async def _delete_file(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """删除文件"""
        file_path = self._resolve_path(parameters['file_path'])
        
        if not file_path.exists():
            return {'status': 'error', 'message': 'File not found'}
        
        file_path.unlink()
        
        return {
            'status': 'success',
            'file_path': str(file_path),
            'message': 'File deleted successfully'
        }
    
    async def _list_files(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """列出目录中的文件"""
        dir_path = self._resolve_path(parameters.get('directory', '.'))
        pattern = parameters.get('pattern', '*')
        recursive = parameters.get('recursive', False)
        
        if not dir_path.is_dir():
            return {'status': 'error', 'message': 'Directory not found'}
        
        files = []
        if recursive:
            for file_path in dir_path.rglob(pattern):
                if file_path.is_file():
                    files.append(self._get_file_info_dict(file_path))
        else:
            for file_path in dir_path.glob(pattern):
                if file_path.is_file():
                    files.append(self._get_file_info_dict(file_path))
        
        return {
            'status': 'success',
            'directory': str(dir_path),
            'files': files,
            'count': len(files)
        }
    
    def _resolve_path(self, path: str) -> Path:
        """解析文件路径"""
        path_obj = Path(path)
        if path_obj.is_absolute():
            return path_obj
        else:
            return self.base_path / path_obj
    
    def _get_file_info_dict(self, file_path: Path) -> Dict[str, Any]:
        """获取文件信息字典"""
        stat = file_path.stat()
        return {
            'name': file_path.name,
            'path': str(file_path),
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'extension': file_path.suffix,
            'mime_type': mimetypes.guess_type(str(file_path))[0]
        }
```

### 数据库操作模块
```python
import asyncio
import aiosqlite
import asyncpg
from typing import Dict, Any, List, Optional, Union
import json

class DatabaseOperations(BaseOperation):
    """数据库操作模块"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.db_type = config.get('type', 'sqlite')
        self.connection_string = config.get('connection_string')
        self.connection = None
        
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据库操作"""
        if not self.validate_parameters(operation, parameters):
            return {'status': 'error', 'message': 'Invalid parameters'}
        
        try:
            await self._ensure_connection()
            
            if operation == 'query':
                return await self._execute_query(parameters)
            elif operation == 'insert':
                return await self._execute_insert(parameters)
            elif operation == 'update':
                return await self._execute_update(parameters)
            elif operation == 'delete':
                return await self._execute_delete(parameters)
            elif operation == 'schema':
                return await self._get_schema(parameters)
            else:
                return {'status': 'error', 'message': f'Unsupported operation: {operation}'}
                
        except Exception as e:
            self.logger.error(f"Database operation error: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def get_supported_operations(self) -> List[str]:
        return ['query', 'insert', 'update', 'delete', 'schema']
    
    async def _ensure_connection(self):
        """确保数据库连接"""
        if self.connection is None:
            if self.db_type == 'sqlite':
                self.connection = await aiosqlite.connect(self.connection_string)
            elif self.db_type == 'postgresql':
                self.connection = await asyncpg.connect(self.connection_string)
    
    async def _execute_query(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询操作"""
        query = parameters['query']
        params = parameters.get('params', [])
        
        if self.db_type == 'sqlite':
            async with self.connection.execute(query, params) as cursor:
                rows = await cursor.fetchall()
                columns = [description[0] for description in cursor.description]
        elif self.db_type == 'postgresql':
            rows = await self.connection.fetch(query, *params)
            columns = list(rows[0].keys()) if rows else []
        
        return {
            'status': 'success',
            'rows': [dict(zip(columns, row)) for row in rows],
            'count': len(rows),
            'columns': columns
        }
```

## 🤖 AI客户端实现

### AI API客户端
```python
import aiohttp
import json
from typing import Dict, Any, Optional
import asyncio

class AIClient:
    """AI API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_url = config['api_url']
        self.api_key = config['api_key']
        self.model = config.get('model', 'gpt-4')
        self.max_tokens = config.get('max_tokens', 4000)
        self.temperature = config.get('temperature', 0.7)
        self.system_prompt = self._load_system_prompt()
        
    def _load_system_prompt(self) -> str:
        """加载系统提示词"""
        # 这里可以从配置文件加载
        return """
        你是HyAIAgent框架中的智能决策引擎。请严格按照JSON格式返回响应。
        """
    
    async def analyze_and_decide(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析上下文并做出决策"""
        try:
            # 构建请求消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": json.dumps(context, ensure_ascii=False, indent=2)}
            ]
            
            # 调用AI API
            response = await self._call_api(messages)
            
            # 解析响应
            return self._parse_response(response)
            
        except Exception as e:
            self.logger.error(f"AI client error: {e}")
            return self._create_error_response(str(e))
    
    async def _call_api(self, messages: List[Dict[str, str]]) -> str:
        """调用AI API"""
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.api_url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data['choices'][0]['message']['content']
                else:
                    raise Exception(f"API call failed: {response.status}")
    
    def _parse_response(self, response: str) -> Dict[str, Any]:
        """解析AI响应"""
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果不是有效JSON，尝试提取JSON部分
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return self._create_error_response("Invalid JSON response")
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "analysis": {
                "understanding": "Error occurred during processing",
                "complexity_assessment": "error",
                "confidence_level": 0.0
            },
            "user_response": {
                "type": "error",
                "content": f"处理请求时发生错误: {error_message}"
            },
            "task_operations": {},
            "ai_cache_operations": {},
            "operation_commands": [],
            "next_action": {
                "type": "wait_for_user",
                "message": "请重新尝试或提供更多信息"
            }
        }
```

---

**📝 说明**: 这个技术实现指南提供了HyAIAgent框架的核心代码实现示例，包括项目结构、操作模块、AI客户端等关键组件的详细实现。
