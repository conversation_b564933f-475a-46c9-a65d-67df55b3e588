# 🤖 HyAIAgent 框架设计文档

## 📖 项目概述

HyAIAgent是一个自主思考、自主决策的AI助手框架，能够根据用户输入自动拆分任务、调用AI API、执行本地操作，并持续迭代优化直到完成目标。

### 🎯 V1版本核心特性
- **自主任务分解** - AI自动将复杂任务拆分为可执行的子任务
- **多AI模型支持** - 支持OpenAI、Anthropic等多种AI模型
- **基础操作能力** - 支持文件操作、数据库操作、网络搜索(Tavily)
- **轻量级存储** - 基于SQLite的本地数据存储
- **简洁架构** - 专注核心功能，避免过度设计

### 🚫 V1版本不包含的功能
- Office文档处理（预留扩展接口）
- 自动化脚本执行
- 插件系统和生态建设
- Web界面和移动适配
- 主题系统和国际化
- 自适应学习和知识图谱
- 智能调度（由AI端点决定）

## 🏗️ 系统架构

### V1版本精简架构
```
┌─────────────────────────────────────────────────────────────┐
│                 HyAIAgent V1 Framework                      │
├─────────────────────────────────────────────────────────────┤
│  🧠 AI Client Manager (AI客户端管理)                        │
│  ├── Multi-AI Support (多AI模型支持)                        │
│  ├── OpenAI Client (OpenAI客户端)                           │
│  ├── Anthropic Client (Anthropic客户端)                     │
│  └── Request Router (请求路由器)                            │
├─────────────────────────────────────────────────────────────┤
│  📋 Task Management (任务管理)                              │
│  ├── Task Queue (任务队列)                                  │
│  ├── Task Executor (任务执行器)                             │
│  └── Progress Monitor (进度监控)                            │
├─────────────────────────────────────────────────────────────┤
│  🔧 Core Operations (核心操作)                              │
│  ├── File Operations (文件操作)                             │
│  ├── Database Operations (SQLite操作)                       │
│  ├── Tavily Search (Tavily网络搜索)                         │
│  └── Cache Operations (缓存操作)                            │
├─────────────────────────────────────────────────────────────┤
│  💾 SQLite Storage (SQLite存储)                             │
│  ├── Task Records (任务记录)                                │
│  ├── Conversation History (对话历史)                        │
│  ├── Cache Data (缓存数据)                                  │
│  └── Configuration (配置信息)                               │
├─────────────────────────────────────────────────────────────┤
│  🌐 Communication (通信层)                                  │
│  ├── JSON Protocol (JSON协议)                               │
│  ├── Response Parser (响应解析)                             │
│  └── Error Handler (错误处理)                               │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 工作流程

### 基础工作流程
```mermaid
graph TD
    A[用户输入] --> B[AI决策引擎]
    B --> C{任务类型判断}
    C -->|简单对话| D[直接AI回复]
    C -->|文件操作| E[文件操作模块]
    C -->|数据查询| F[数据库操作模块]
    C -->|网络搜索| G[搜索操作模块]
    C -->|复杂任务| H[任务分解]
    
    E --> I[执行结果]
    F --> I
    G --> I
    H --> J[任务队列]
    J --> K[任务执行器]
    K --> I
    
    I --> L[结果整合]
    L --> M[AI总结回复]
    M --> N[返回用户]
    
    D --> N
```

## 📡 通信协议

### AI交互JSON协议
```json
{
  "request": {
    "type": "task_analysis",
    "user_input": "用户的原始输入",
    "context": {
      "conversation_history": [],
      "available_operations": ["file", "database", "search"],
      "current_workspace": "/path/to/workspace"
    }
  },
  "response": {
    "analysis": {
      "task_type": "complex_task",
      "confidence": 0.9,
      "reasoning": "任务分析的推理过程"
    },
    "execution_plan": {
      "tasks": [
        {
          "id": "task_001",
          "type": "file_operation",
          "operation": "read_file",
          "parameters": {
            "file_path": "example.txt"
          },
          "priority": 1,
          "dependencies": []
        }
      ]
    },
    "expected_outcome": "预期的执行结果描述"
  }
}
```

### 操作执行协议
```json
{
  "operation": {
    "type": "file_operation",
    "action": "read_file",
    "parameters": {
      "file_path": "example.txt",
      "encoding": "utf-8"
    }
  },
  "result": {
    "status": "success",
    "data": {
      "content": "文件内容",
      "file_size": 1024,
      "last_modified": "2024-01-01T12:00:00Z"
    },
    "execution_time": 0.05
  }
}
```

## 🔧 核心模块设计

### 1. AI客户端管理器
```python
class AIClientManager:
    """多AI模型客户端管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.default_provider = AIProvider.OPENAI
    
    async def chat_completion(self, messages: list, provider: Optional[AIProvider] = None) -> str:
        """统一的聊天完成接口"""
        pass
    
    def get_available_providers(self) -> list:
        """获取可用的AI提供商"""
        pass
```

### 2. 任务管理系统
```python
class TaskManager:
    """任务管理器"""
    
    def __init__(self, database: HyAIDatabase):
        self.database = database
        self.task_queue = asyncio.Queue()
        self.running_tasks = {}
    
    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        pass
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """执行任务"""
        pass
```

### 3. 操作模块基类
```python
class BaseOperation:
    """操作模块基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行操作"""
        raise NotImplementedError
    
    def get_supported_operations(self) -> List[str]:
        """获取支持的操作列表"""
        raise NotImplementedError
```

## 💾 数据存储设计

### SQLite数据库表结构
```sql
-- 任务表
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL,
    description TEXT,
    parameters TEXT, -- JSON格式
    status TEXT DEFAULT 'pending',
    priority INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    result TEXT -- JSON格式
);

-- 对话历史表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL, -- user/assistant
    content TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ai_provider TEXT,
    tokens_used INTEGER
);

-- AI缓存表
CREATE TABLE ai_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    tags TEXT, -- JSON数组
    importance REAL DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    use_count INTEGER DEFAULT 0
);
```

## 🔍 AI提示词模板

### 系统提示词
```
你是HyAIAgent框架中的智能决策引擎。你的任务是：

1. 分析用户输入，理解用户的真实需求
2. 根据可用的操作模块，制定执行计划
3. 将复杂任务分解为可执行的子任务
4. 返回结构化的JSON响应

可用操作模块：
- file_operations: 文件读写、删除、列表等操作
- database_operations: 数据库查询、插入、更新等操作
- search_operations: 网络搜索和信息获取
- cache_operations: 缓存管理和知识存储

请始终：
- 保持安全性，不执行危险操作
- 提供清晰的执行计划和预期结果
- 使用标准的JSON格式返回响应
- 考虑任务的优先级和依赖关系
```

### 任务分析提示词
```
请分析以下用户请求，并制定详细的执行计划：

用户输入：{user_input}

当前上下文：
- 工作目录：{workspace}
- 可用操作：{available_operations}
- 对话历史：{conversation_summary}

请返回JSON格式的分析结果，包括：
1. 任务类型和复杂度评估
2. 详细的执行步骤
3. 每个步骤的参数和预期结果
4. 任务间的依赖关系
```

## 🛡️ 安全机制

### 文件操作安全
- 限制操作范围在指定工作目录内
- 检查文件扩展名白名单
- 限制文件大小和操作频率
- 记录所有文件操作日志

### API调用安全
- 实施速率限制
- 验证API响应格式
- 处理API错误和超时
- 保护API密钥安全

### 数据安全
- 敏感数据加密存储
- 定期清理过期数据
- 备份重要数据
- 访问权限控制

## 📈 性能优化

### 缓存策略
- AI响应结果缓存
- 文件内容缓存
- 搜索结果缓存
- 智能缓存清理

### 并发处理
- 异步任务执行
- 连接池管理
- 资源限制控制
- 负载均衡

## 🔮 扩展性设计

### 预留扩展接口
```python
class ExtensionInterface:
    """扩展接口"""
    
    def get_name(self) -> str:
        """获取扩展名称"""
        pass
    
    def get_operations(self) -> List[str]:
        """获取支持的操作"""
        pass
    
    async def execute_operation(self, operation: str, parameters: Dict) -> Dict:
        """执行操作"""
        pass
```

### 未来扩展方向
- Office文档处理模块
- 邮件集成模块
- 日历管理模块
- 自动化脚本执行
- 机器学习模型集成

---

**🎯 设计目标**: 构建一个简洁、高效、可扩展的AI助手框架，为用户提供智能化的自动化解决方案。
