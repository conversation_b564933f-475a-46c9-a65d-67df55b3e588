# 🚀 HyAIAgent V1版本实现指南

## 📋 V1版本目标

专注于核心功能，构建一个简洁、实用的自主AI助手框架，满足个人使用需求。

### ✅ V1版本包含功能
- **多AI模型支持** - OpenAI、Anthropic等
- **基础文件操作** - 读写、删除、列表等
- **SQLite数据库** - 轻量级本地存储
- **Tavily网络搜索** - 集成Tavily搜索API
- **任务管理** - 基础任务队列和执行
- **对话历史** - 简单的对话记录和缓存
- **桌面界面** - PyQt6简洁界面

### ❌ V1版本不包含功能
- Office文档处理（预留接口）
- 插件系统和生态
- Web界面和移动端
- 自适应学习和知识图谱
- 复杂的智能调度
- 国际化和主题系统

## 🏗️ 项目结构（精简版）

```
HyAIAgent/
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── agent.py                   # 主控制器
│   ├── ai_client.py              # AI客户端管理
│   ├── task_manager.py           # 任务管理
│   └── database.py               # SQLite数据库
├── operations/                    # 操作模块
│   ├── __init__.py
│   ├── file_ops.py               # 文件操作
│   ├── db_ops.py                 # 数据库操作
│   ├── search_ops.py             # Tavily搜索
│   └── cache_ops.py              # 缓存操作
├── ui/                           # 用户界面
│   ├── __init__.py
│   ├── main_window.py           # 主窗口
│   └── chat_widget.py           # 聊天组件
├── utils/                        # 工具类
│   ├── __init__.py
│   ├── config.py                # 配置管理
│   ├── logger.py                # 日志工具
│   └── helpers.py               # 辅助函数
├── config/                       # 配置文件
│   ├── config.json              # 主配置
│   └── prompts.json             # 提示词模板
├── data/                         # 数据目录
│   └── hyaiagent.db             # SQLite数据库
├── requirements.txt              # 依赖包
└── main.py                      # 程序入口
```

## 🔧 核心依赖包

```txt
# 核心框架
asyncio>=3.4.3
aiohttp>=3.8.0
aiofiles>=0.8.0

# AI客户端
openai>=1.0.0
anthropic>=0.7.0

# 数据库
aiosqlite>=0.18.0

# 网络搜索
tavily-python>=0.3.0

# GUI界面
PyQt6>=6.4.0

# 工具库
python-dotenv>=1.0.0
loguru>=0.6.0
pydantic>=1.10.0
```

## 🤖 多AI模型支持实现

### AI客户端管理器
```python
import asyncio
import openai
import anthropic
from typing import Dict, Any, Optional
from enum import Enum

class AIProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

class AIClientManager:
    """多AI模型客户端管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.default_provider = AIProvider.OPENAI
        self._init_clients()
    
    def _init_clients(self):
        """初始化AI客户端"""
        # OpenAI客户端
        if self.config.get('openai', {}).get('api_key'):
            self.clients[AIProvider.OPENAI] = openai.AsyncOpenAI(
                api_key=self.config['openai']['api_key']
            )
        
        # Anthropic客户端
        if self.config.get('anthropic', {}).get('api_key'):
            self.clients[AIProvider.ANTHROPIC] = anthropic.AsyncAnthropic(
                api_key=self.config['anthropic']['api_key']
            )
    
    async def chat_completion(self, 
                            messages: list, 
                            provider: Optional[AIProvider] = None,
                            **kwargs) -> str:
        """统一的聊天完成接口"""
        provider = provider or self.default_provider
        
        if provider not in self.clients:
            raise ValueError(f"AI provider {provider.value} not configured")
        
        if provider == AIProvider.OPENAI:
            return await self._openai_completion(messages, **kwargs)
        elif provider == AIProvider.ANTHROPIC:
            return await self._anthropic_completion(messages, **kwargs)
    
    async def _openai_completion(self, messages: list, **kwargs) -> str:
        """OpenAI聊天完成"""
        client = self.clients[AIProvider.OPENAI]
        
        response = await client.chat.completions.create(
            model=kwargs.get('model', 'gpt-4'),
            messages=messages,
            max_tokens=kwargs.get('max_tokens', 4000),
            temperature=kwargs.get('temperature', 0.7)
        )
        
        return response.choices[0].message.content
    
    async def _anthropic_completion(self, messages: list, **kwargs) -> str:
        """Anthropic聊天完成"""
        client = self.clients[AIProvider.ANTHROPIC]
        
        # 转换消息格式
        system_message = ""
        user_messages = []
        
        for msg in messages:
            if msg['role'] == 'system':
                system_message = msg['content']
            else:
                user_messages.append(msg)
        
        response = await client.messages.create(
            model=kwargs.get('model', 'claude-3-sonnet-20240229'),
            max_tokens=kwargs.get('max_tokens', 4000),
            temperature=kwargs.get('temperature', 0.7),
            system=system_message,
            messages=user_messages
        )
        
        return response.content[0].text
    
    def get_available_providers(self) -> list:
        """获取可用的AI提供商"""
        return list(self.clients.keys())
    
    def set_default_provider(self, provider: AIProvider):
        """设置默认AI提供商"""
        if provider in self.clients:
            self.default_provider = provider
        else:
            raise ValueError(f"Provider {provider.value} not available")
```

## 💾 SQLite数据库设计

### 数据库模式
```sql
-- 任务表
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL,
    description TEXT,
    parameters TEXT, -- JSON格式
    status TEXT DEFAULT 'pending',
    priority INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    result TEXT -- JSON格式
);

-- 对话历史表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL, -- user/assistant
    content TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ai_provider TEXT,
    tokens_used INTEGER
);

-- AI缓存表
CREATE TABLE ai_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    tags TEXT, -- JSON数组
    importance REAL DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    use_count INTEGER DEFAULT 0
);

-- 配置表
CREATE TABLE config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 搜索历史表
CREATE TABLE search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    query TEXT NOT NULL,
    provider TEXT DEFAULT 'tavily',
    results TEXT, -- JSON格式
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 数据库操作类
```python
import aiosqlite
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional

class HyAIDatabase:
    """HyAIAgent SQLite数据库管理"""
    
    def __init__(self, db_path: str = "data/hyaiagent.db"):
        self.db_path = db_path
        self.connection = None
    
    async def init_database(self):
        """初始化数据库"""
        self.connection = await aiosqlite.connect(self.db_path)
        await self._create_tables()
    
    async def _create_tables(self):
        """创建数据库表"""
        # 这里包含上面的SQL创建语句
        pass
    
    # 任务相关操作
    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        async with self.connection.execute(
            """INSERT INTO tasks (task_id, type, description, parameters, priority)
               VALUES (?, ?, ?, ?, ?)""",
            (task_data['task_id'], task_data['type'], task_data['description'],
             json.dumps(task_data.get('parameters', {})), task_data.get('priority', 5))
        ) as cursor:
            await self.connection.commit()
            return task_data['task_id']
    
    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务"""
        async with self.connection.execute(
            "SELECT * FROM tasks WHERE status IN ('pending', 'running') ORDER BY priority DESC"
        ) as cursor:
            rows = await cursor.fetchall()
            return [self._row_to_dict(row, cursor.description) for row in rows]
    
    # 对话历史操作
    async def save_conversation(self, session_id: str, role: str, content: str, 
                              ai_provider: str = None, tokens_used: int = None):
        """保存对话记录"""
        await self.connection.execute(
            """INSERT INTO conversations (session_id, role, content, ai_provider, tokens_used)
               VALUES (?, ?, ?, ?, ?)""",
            (session_id, role, content, ai_provider, tokens_used)
        )
        await self.connection.commit()
    
    async def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取对话历史"""
        async with self.connection.execute(
            """SELECT * FROM conversations WHERE session_id = ? 
               ORDER BY timestamp DESC LIMIT ?""",
            (session_id, limit)
        ) as cursor:
            rows = await cursor.fetchall()
            return [self._row_to_dict(row, cursor.description) for row in rows]
    
    # AI缓存操作
    async def cache_knowledge(self, key: str, content: str, tags: List[str] = None, 
                            importance: float = 0.5):
        """缓存AI知识"""
        await self.connection.execute(
            """INSERT OR REPLACE INTO ai_cache (cache_key, content, tags, importance)
               VALUES (?, ?, ?, ?)""",
            (key, content, json.dumps(tags or []), importance)
        )
        await self.connection.commit()
    
    async def search_cache(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索缓存知识"""
        # 简单的文本搜索，可以后续优化为向量搜索
        async with self.connection.execute(
            """SELECT * FROM ai_cache 
               WHERE content LIKE ? OR tags LIKE ?
               ORDER BY importance DESC, use_count DESC LIMIT ?""",
            (f'%{query}%', f'%{query}%', limit)
        ) as cursor:
            rows = await cursor.fetchall()
            return [self._row_to_dict(row, cursor.description) for row in rows]
    
    def _row_to_dict(self, row, description) -> Dict[str, Any]:
        """将数据库行转换为字典"""
        return {desc[0]: row[i] for i, desc in enumerate(description)}
    
    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            await self.connection.close()
```

## 🔍 Tavily搜索集成

### Tavily搜索操作
```python
from tavily import TavilyClient
from typing import Dict, Any, List

class TavilySearchOperations:
    """Tavily网络搜索操作"""
    
    def __init__(self, api_key: str):
        self.client = TavilyClient(api_key=api_key)
    
    async def search(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """执行网络搜索"""
        try:
            response = self.client.search(
                query=query,
                search_depth="basic",
                max_results=max_results,
                include_answer=True,
                include_raw_content=False
            )
            
            return {
                'status': 'success',
                'query': query,
                'answer': response.get('answer', ''),
                'results': response.get('results', []),
                'total_results': len(response.get('results', []))
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'query': query,
                'error': str(e),
                'results': []
            }
    
    async def get_search_context(self, query: str) -> str:
        """获取搜索上下文摘要"""
        result = await self.search(query, max_results=3)
        
        if result['status'] == 'success':
            context = f"搜索查询: {query}\n"
            if result.get('answer'):
                context += f"答案摘要: {result['answer']}\n"
            
            context += "相关结果:\n"
            for i, item in enumerate(result['results'][:3], 1):
                context += f"{i}. {item.get('title', '')}\n"
                context += f"   {item.get('content', '')[:200]}...\n"
            
            return context
        else:
            return f"搜索失败: {result.get('error', '未知错误')}"
```

---

**🎯 V1版本重点**: 专注核心功能，确保稳定可用，为后续扩展打好基础。
